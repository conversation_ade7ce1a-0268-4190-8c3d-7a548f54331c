#!/usr/bin/env python3
"""
Redis连接测试脚本
用于诊断Redis连接问题
"""
import redis
import socket
import time

def test_redis_connection():
    """测试Redis连接"""
    print("🔍 Redis连接诊断工具")
    print("=" * 50)
    
    # 基本网络连接测试
    print("\n1. 测试网络连接...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex(('localhost', 6379))
        sock.close()
        
        if result == 0:
            print("✅ 网络连接正常 (端口6379可达)")
        else:
            print(f"❌ 网络连接失败 (错误代码: {result})")
            return
    except Exception as e:
        print(f"❌ 网络测试异常: {e}")
        return
    
    # Redis连接测试
    print("\n2. 测试Redis连接...")
    
    test_configs = [
        {
            "name": "无密码连接",
            "config": {"host": "localhost", "port": 6379, "db": 0}
        },
        {
            "name": "用户名+密码连接",
            "config": {"host": "localhost", "port": 6379, "db": 0, "username": "default", "password": "zqdl@redis"}
        },
        {
            "name": "仅密码连接",
            "config": {"host": "localhost", "port": 6379, "db": 0, "password": "zqdl@redis"}
        },
        {
            "name": "URL连接",
            "config": {"url": "redis://default:zqdl@redis@localhost:6379/0"}
        }
    ]
    
    for test_config in test_configs:
        print(f"\n   测试: {test_config['name']}")
        try:
            if "url" in test_config["config"]:
                client = redis.from_url(test_config["config"]["url"], socket_connect_timeout=5)
            else:
                client = redis.Redis(**test_config["config"], socket_connect_timeout=5)
            
            # 尝试ping
            result = client.ping()
            print(f"   ✅ 连接成功! Ping结果: {result}")
            
            # 尝试基本操作
            client.set("test_key", "test_value", ex=10)
            value = client.get("test_key")
            print(f"   ✅ 读写测试成功: {value}")
            
            client.delete("test_key")
            print(f"   ✅ 删除测试成功")
            
            client.close()
            print(f"   ✅ {test_config['name']} 完全正常!")
            return True
            
        except redis.ConnectionError as e:
            print(f"   ❌ 连接错误: {e}")
        except redis.AuthenticationError as e:
            print(f"   ❌ 认证错误: {e}")
        except Exception as e:
            print(f"   ❌ 其他错误: {e}")
    
    print("\n❌ 所有连接方式都失败了")
    
    # 提供解决建议
    print("\n🔧 可能的解决方案:")
    print("1. 检查Redis Docker容器状态: docker ps | grep redis")
    print("2. 检查Redis日志: docker logs <redis_container_id>")
    print("3. 检查Redis配置文件中的bind设置")
    print("4. 尝试重启Redis容器: docker restart <redis_container_id>")
    print("5. 检查防火墙设置: sudo ufw status")
    print("6. 尝试从容器内部连接: docker exec -it <redis_container_id> redis-cli")
    
    return False

if __name__ == "__main__":
    test_redis_connection()
